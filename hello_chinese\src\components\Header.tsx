
import React from 'react'

const Navbar = () => {
  return (
    <header className="bg-white shadow-md">
      <nav className="max-w-7xl mx-auto px-4 py-4 flex justify-between items-center">
        <div className="text-xl font-bold text-gray-800">Hello Chinese</div>
        <ul className="flex space-x-6">
            {/* <li>
                <Link href="/" className="text-gray-600 hover:text-blue-500">
                Home
                </Link>
            </li>
            <li>
                <Link href="/lessons" className="text-gray-600 hover:text-blue-500">
                Lessons
                </Link>
            </li>
            <li>
                <Link href="/about" className="text-gray-600 hover:text-blue-500">
                About
                </Link>
            </li>
            <li>
                <Link href="/contact" className="text-gray-600 hover:text-blue-500">
                Contact
                </Link>
            </li> */}
        </ul>
      </nav>
    </header>
  )
}

export default Navbar