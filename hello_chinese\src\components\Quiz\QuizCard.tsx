'use client'
import { useRef } from 'react'
import Question from './Question'
import AnswerButton from './AnswerButton'

type QuizCardProps = {
    question: string
    pinyin: string
    options: string[]
    audioSrc: string
    onSelect: (option: string) => void
}

export default function QuizCard({ pinyin, question, options, audioSrc, onSelect }: QuizCardProps) {
    const audioRef = useRef<HTMLAudioElement | null>(null)

    const playAudio = () => {
        audioRef.current?.play()
    }

    
    return (
        <div className="p-6 space-y-6 max-w-xl w-full mx-auto">
            <div className="flex justify-between items-center">
                <Question text={question} title={pinyin}/>
                <button onClick={playAudio} className="text-xl cursor-pointer" title="Play Audio">
                    🔊
                </button>
            </div>
            <div className="grid grid-cols-1 gap-2">
                {options.map((opt, idx) => (
                    <AnswerButton key={idx} text={opt} onClick={() => onSelect(opt)} />
                ))}
            </div>
            <audio ref={audioRef} src={audioSrc} hidden />
        </div>
    )
}
