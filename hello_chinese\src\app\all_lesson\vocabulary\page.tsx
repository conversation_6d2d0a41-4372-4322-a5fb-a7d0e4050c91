'use client'
import { useRef } from 'react'

const vocabulary = [
    { chinese: "你好", pinyin: "nǐ hǎo", meaning: "Hello", audio: "/audio/ni_hao.mp3" },
    { chinese: "谢谢", pinyin: "xi<PERSON> xiè", meaning: "Thank you", audio: "/audio/xiexie.mp3" },
    { chinese: "再见", pinyin: "zài jiàn", meaning: "Goodbye", audio: "/audio/zaijian.mp3" },
    { chinese: "请", pinyin: "qǐng", meaning: "Please", audio: "/audio/qing.mp3" },
    { chinese: "对不起", pinyin: "duì bù qǐ", meaning: "Sorry", audio: "/audio/duibuqi.mp3" },
    { chinese: "没关系", pinyin: "méi guān xì", meaning: "It's okay", audio: "/audio/meiguanxi.mp3" },
    { chinese: "是的", pinyin: "shì de", meaning: "Yes", audio: "/audio/shide.mp3" },
    { chinese: "不是", pinyin: "bù shì", meaning: "No", audio: "/audio/bushi.mp3" },
    { chinese: "我爱你", pinyin: "wǒ ài nǐ", meaning: "I love you", audio: "/audio/woaini.mp3" },
    { chinese: "祝你好运", pinyin: "zhù nǐ hǎo yùn", meaning: "Good luck to you", audio: "/audio/zunihauyun.mp3" }
]

export default function LearnPage() {
    const audioRef = useRef<HTMLAudioElement | null>(null)

    const playSound = (src: string) => {
        if (audioRef.current) {
            audioRef.current.src = src
            audioRef.current.play()
        }
    }

    return (
        <div className="p-4 max-w-md mx-auto">
            <h1 className="text-2xl font-bold mb-4">🧠 Learn Chinese Vocabulary</h1>
            <ul className="space-y-4">
                {vocabulary.map((item, index) => (
                    <li key={index} className="border rounded-xl p-4 shadow bg-white">
                        <div className="flex justify-between items-center">
                            <div>
                                <p className="text-xl font-semibold">{item.chinese}</p>
                                <p className="text-gray-500 italic">{item.pinyin}</p>
                                <p className="text-blue-600">{item.meaning}</p>
                            </div>
                            <button
                                onClick={() => playSound(item.audio)}
                                className="bg-blue-500 cursor-pointer text-white px-3 py-1 rounded"
                            >
                                🔈
                            </button>
                        </div>
                    </li>
                ))}
            </ul>
            <audio ref={audioRef} hidden />
        </div>
    )
}
