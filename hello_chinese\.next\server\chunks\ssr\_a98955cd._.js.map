{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Hello_Chinese_HSK/hello_chinese/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Hello_Chinese_HSK/hello_chinese/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Hello_Chinese_HSK/hello_chinese/src/app/dashboard/page.tsx"], "sourcesContent": ["\r\nimport { Card } from '@/components/ui/card'\r\nimport Link from 'next/link'\r\nimport React,{ useState , useEffect} from 'react'\r\n\r\nconst Dashboard = () => {\r\n  const [clicklessons, setClickLessons] = useState([])\r\n  const [clickquiz, setClickQuiz] = useState([])\r\n\r\n  useEffect(()=>{\r\n    const lessons = JSON.parse(localStorage.getItem('clicklessons') || '[]')\r\n    const quiz = JSON.parse(localStorage.getItem('clickquiz') || '[]')\r\n\r\n    setClickLessons(lessons)\r\n    setClickQuiz(quiz)\r\n  },[])\r\n\r\n  if (clicklessons.length === 0 && clickquiz.length === 0) {\r\n    return (\r\n      <div className=\"flex flex-col items-center justify-center h-screen\">\r\n        <h1 className=\"text-2xl font-bold mb-4\">Welcome to the Dashboard</h1>\r\n        <p className=\"text-gray-600\">Please select a lesson or quiz to get started.</p>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div className=\" flex flex-col md:flex-row gap-4 justify-center items-center px-4 md:px-10 md:mt-32 mt-10 \">\r\n      <Link href='/all_lesson'>\r\n        <Card className=' w-[250px] cursor-pointer overflow-hidden transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-xl'>\r\n          <div className=\"p-4\">\r\n            <h2 className=\"text-lg font-semibold mb-2\">All Lessons</h2>\r\n            <p className=\"text-gray-600 mb-4\">\r\n              Explore all available lessons to enhance your Chinese language skills.\r\n            </p>\r\n          </div>\r\n        </Card>\r\n      </Link>\r\n      {}\r\n      <Link href='/quiz'>\r\n        <Card className=' w-[250px] cursor-pointer overflow-hidden transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-xl'>\r\n          <div className=\"p-4\">\r\n            <h2 className=\"text-lg font-semibold mb-2\">Lesson Quiz</h2>\r\n            <p className=\"text-gray-600 mb-4\">\r\n              Test your knowledge with quizzes based on the lessons.\r\n            </p>\r\n          </div>\r\n        </Card>\r\n      </Link>\r\n        <Card className=' w-[250px] cursor-pointer overflow-hidden transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-xl'>\r\n          <div className=\"p-4\">\r\n            <h2 className=\"text-lg font-semibold mb-2\">Lesson Title</h2>\r\n            <p className=\"text-gray-600 mb-4\">This is a brief description of the lesson content.</p>\r\n          </div>\r\n        </Card>\r\n        <Card className=' w-[250px] cursor-pointer overflow-hidden transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-xl'>\r\n          <div className=\"p-4\">\r\n            <h2 className=\"text-lg font-semibold mb-2\">Lesson Title</h2>\r\n            <p className=\"text-gray-600 mb-4\">This is a brief description of the lesson content.</p>\r\n          </div>\r\n        </Card>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default Dashboard\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAEA,MAAM,YAAY;IAChB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,UAAU,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,mBAAmB;QACnE,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAE7D,gBAAgB;QAChB,aAAa;IACf,GAAE,EAAE;IAEJ,IAAI,aAAa,MAAM,KAAK,KAAK,UAAU,MAAM,KAAK,GAAG;QACvD,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA0B;;;;;;8BACxC,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAGnC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4JAAA,CAAA,UAAI;gBAAC,MAAK;0BACT,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;;;;;;;;;;;0BAOxC,8OAAC,4JAAA,CAAA,UAAI;gBAAC,MAAK;0BACT,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;;;;;;;;;;;0BAMtC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;;;;0BAGtC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;;;;;;;;;;AAK9C;uCAEe", "debugId": null}}, {"offset": {"line": 343, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Hello_Chinese_HSK/hello_chinese/src/app/page.tsx"], "sourcesContent": ["\nimport Dashboard from \"./dashboard/page\";\n\n\nexport default function Home() {\n  return (\n   <>\n    <Dashboard />\n   </>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAGe,SAAS;IACtB,qBACC;kBACC,cAAA,8OAAC,gIAAA,CAAA,UAAS;;;;;;AAGd", "debugId": null}}]}