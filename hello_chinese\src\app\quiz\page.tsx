/* eslint-disable react/no-unescaped-entities */
/* eslint-disable @typescript-eslint/no-unused-vars */
'use client'
import QuizCard from '@/components/Quiz/QuizCard'
import { useEffect, useState } from 'react'
import questions from '../../components/data/questions.json'
import { motion } from 'framer-motion'
import { Card } from '@/components/ui/card'

const levels = ['beginner', 'intermediate', 'advanced'] as const 
type Difficulty = typeof levels[number]

export default function QuizPage() {
    const [selectedLevel, setSelectedLevel] = useState<Difficulty | null>(null)
    const [current, setCurrent] = useState(0)
    const [score, setScore] = useState(0)
    const [totalAttempts, setTotalAttempts] = useState(0)
    const [feedback, setFeedback] = useState<'correct' | 'wrong' | null>(null)
    const [showResult, setShowResult] = useState(false)
    const [timeLeft, setTimeLeft] = useState(10) // seconds

    const filteredQuestions = questions.filter(q => q.difficulty === selectedLevel)

    const currentQuestion = filteredQuestions[current]

    const handleAnswer = (option: string) => {
        const isCorrect = option === currentQuestion.answer
        setFeedback(isCorrect ? 'correct' : 'wrong')
        if (isCorrect) setScore(score + 1)
        setTotalAttempts(totalAttempts + 1)

        setTimeout(() => {
            setFeedback(null)
            const next = current + 1
            if (next < filteredQuestions.length) {
                setCurrent(next)
                setTimeLeft(10)
            } else {
                setShowResult(true)
            }
        }, 1000)
    }

    useEffect(() => {
        if (showResult) {
            const result = {
                score,
                total: totalAttempts,
                timestamp: new Date().toISOString(),
            }
            localStorage.setItem('chineseQuizResult', JSON.stringify(result))
        }
    }, [showResult, score, totalAttempts])

    // Timer countdown logic
    useEffect(() => {
        if (timeLeft === 0) {
            handleAnswer('') // skip if no answer
            return
        }
        const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000)
        return () => clearTimeout(timer)
    }, [timeLeft])

    const resetQuiz = () => {
        setCurrent(0)
        setScore(0)
        setTotalAttempts(0)
        setShowResult(false)
        setTimeLeft(10)
        setFeedback(null)
    }

    if (!selectedLevel) {
        return (
            <div className=" flex flex-col justify-center items-center space-y-4 p-4 my-20">
                <h1 className="text-2xl font-bold">🧠 Select Your Level</h1>
                <div className="flex flex-col gap-4 w-full max-w-xs">
                    {levels.map(level => (
                        <button
                            key={level}
                            onClick={() => setSelectedLevel(level)}
                            className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition"
                        >
                            {level}
                        </button>
                    ))}
                </div>
            </div>
        )
    }

    if (selectedLevel && filteredQuestions.length === 0) {
        return (
            <div className="text-center p-6">
                <h2 className="text-xl font-bold">No questions available for "{selectedLevel}"</h2>
                <button
                    onClick={() => setSelectedLevel(null)}
                    className="mt-4 px-4 py-2 bg-blue-600 text-white rounded"
                >
                    🔙 Back to Level Select
                </button>
            </div>
        )
    }


    if (showResult) {
        return (
            <div className='flex items-center justify-center my-10'>
                <Card className="w-[450px] shadow-xl justify-center items-center p-6 text-center space-y-4">
                    <h2 className="text-2xl font-bold">🎉 Quiz Complete!</h2>
                    <p className="text-lg">✅ Correct: {score}</p>
                    <p className="text-lg">❌ Incorrect: {filteredQuestions.length - score}</p>
                    <p className="text-lg">📊 Score: {score}/{filteredQuestions.length}</p>
                    <button
                        onClick={resetQuiz}
                        className="mt-4 px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition cursor-pointer"
                    >
                        🔁 Try Again
                    </button>
                </Card>

            </div>
        )
    }

    const { question, options, audio } = questions[current]

    return (
        <div className="flex items-center justify-center my-5">
            <Card className="justify-center p-1 flex-col w-[450px] h-[500px] shadow-xl">

                <p className="text-lg mt-1 text-center font-semibold">Question {current + 1} of {filteredQuestions.length}</p>

                <div className=" mx-auto flex gap-20 items-center mb-2">
                    <p className="text-sm text-gray-600 capitalize">📘 Level: 
                    <span 
                    className={selectedLevel === 'beginner' ? 'text-green-600' : selectedLevel === 'intermediate' ? 'text-yellow-600' : 'text-red-600'}>
                        <span className={selectedLevel === 'beginner' ? 'bg-green-100' : selectedLevel === 'intermediate' ? 'bg-yellow-100' : 'bg-red-100'}>
                            {selectedLevel}
                        </span>
                    </span>
                    </p>
                    <button
                        onClick={() => {
                            setSelectedLevel(null)
                            resetQuiz()
                        }}
                        className="text-white bg-blue-600 text-sm hover:bg-blue-800 py-2 px-4 rounded cursor-pointer transition"
                    >
                        🔙 Back to Level Select
                    </button>
                </div>


                {/* Progress Bar */}
                <div className="w-[400px] max-w-xl bg-gray-200 h-2 rounded-full mb-1 mx-auto">
                    <div
                        className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${((current + 1) / filteredQuestions.length) * 100}%` }}
                    ></div>
                </div>

                {/* Timer */}
                <p className="text-sm text-center text-gray-500 -translate-y-3">⏳ Time left: {timeLeft}s</p>

                {/* Quiz Content */}
                <div className={`transition duration-300 transform -translate-y-8 ${feedback === 'correct' ? 'scale-105' : feedback === 'wrong' ? 'scale-95' : ''}`}>
                    <motion.div
                        key={current}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.3 }}
                    >
                        <QuizCard
                            question={currentQuestion.question}
                            pinyin={currentQuestion.pinyin}
                            options={currentQuestion.options}
                            audioSrc={currentQuestion.audio}
                            onSelect={handleAnswer}
                        />
                    </motion.div>
                    {feedback && (
                        <div className={`-mt-5 text-center font-semibold ${feedback === 'correct' ? 'text-green-600' : 'text-red-600'}`}>
                            {feedback === 'correct' ? '✅ Correct!' : '❌ Wrong'}
                        </div>
                    )}
                </div>
            </Card>
        </div>
    )
}

