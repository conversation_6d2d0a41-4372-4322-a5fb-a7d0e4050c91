"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { MagicCard } from "@/components/magicui/magic-card";
import { useTheme } from "next-themes";

 function Pinyin_Card() {
  const { theme } = useTheme();
  return (
    <Card className="p-0 max-w-sm w-[250px] shadow-2xl border-none">
      <MagicCard
        gradientColor={theme === "dark" ? "#262626" : "#D9D9D955"}
        className="p-0"
      >
        <CardHeader className="border-b border-border p-4 [.border-b]:pb-4">
          <CardTitle className="text-2xl font-bold">Pinyin</CardTitle>
          <CardDescription>
            <p className="text-sm text-gray-500">
              Learn Pinyin system to improve your Chinese pronunciation.
            </p>
          </CardDescription>
        </CardHeader>
        <CardFooter className="p-4 border-t border-border [.border-t]:pt-4">
          <Button className="w-full bg-blue-600 cursor-pointer">Start Lesson</Button>
        </CardFooter>
      </MagicCard>
    </Card>
  );
}

export default Pinyin_Card;