"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
    Card,
    CardDescription,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    CardTitle,
} from "@/components/ui/card";
import { MagicCard } from "@/components/magicui/magic-card";
import { useTheme } from "next-themes";
import { useRouter } from "next/navigation";

function Vocabulary_Card() {
    const { theme } = useTheme();
    const router = useRouter();
    return (
        <Card className="p-0 max-w-sm w-[250px] shadow-2xl border-none">
            <MagicCard
                gradientColor={theme === "dark" ? "#262626" : "#D9D9D955"}
                className="p-0"
            >
                <CardHeader className="border-b border-border p-4 [.border-b]:pb-4">
                    <CardTitle className="text-2xl font-bold">Vocabulary</CardTitle>
                    <CardDescription>
                        <p className="text-sm text-gray-500">
                            Learn essential Chinese vocabulary with interactive lessons.
                        </p>
                    </CardDescription>
                </CardHeader>
                <CardFooter className="p-4 border-t border-border [.border-t]:pt-4">
                    <Button onClick={() => router.push("/all_lesson/vocabulary")} className="w-full bg-blue-600 cursor-pointer">Start Lesson</Button>
                </CardFooter>
            </MagicCard>
        </Card>
    );
}

export default Vocabulary_Card;