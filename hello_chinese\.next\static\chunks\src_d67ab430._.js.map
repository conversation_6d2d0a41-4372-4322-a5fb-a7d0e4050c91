{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Hello_Chinese_HSK/hello_chinese/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Hello_Chinese_HSK/hello_chinese/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Hello_Chinese_HSK/hello_chinese/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { Card } from '@/components/ui/card'\r\nimport Link from 'next/link'\r\nimport React,{ useState , useEffect} from 'react'\r\n\r\nconst Dashboard = () => {\r\n  const [clickquiz, setClickQuiz] = useState([])\r\n\r\n  useEffect(()=>{\r\n    const quiz = JSON.parse(localStorage.getItem('clickquiz') || '[]')\r\n    setClickQuiz(quiz)\r\n  },[])\r\n\r\n  return (\r\n    <div className=\" flex flex-col md:flex-row gap-4 justify-center items-center px-4 md:px-10 md:mt-32 mt-10 \">\r\n      <Link href='/all_lesson'>\r\n        <Card className=' w-[250px] cursor-pointer overflow-hidden transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-xl'>\r\n          <div className=\"p-4\">\r\n            <h2 className=\"text-lg font-semibold mb-2\">All Lessons</h2>\r\n            <p className=\"text-gray-600 mb-4\">\r\n              Explore all available lessons to enhance your Chinese language skills.\r\n            </p>\r\n          </div>\r\n        </Card>\r\n      </Link>\r\n      {clickquiz}\r\n      <Link href='/quiz'>\r\n        <Card className=' w-[250px] cursor-pointer overflow-hidden transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-xl'>\r\n          <div className=\"p-4\">\r\n            <h2 className=\"text-lg font-semibold mb-2\">Lesson Quiz</h2>\r\n            <p className=\"text-gray-600 mb-4\">\r\n              Test your knowledge with quizzes based on the lessons.\r\n            </p>\r\n          </div>\r\n        </Card>\r\n      </Link>\r\n        <Card className=' w-[250px] cursor-pointer overflow-hidden transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-xl'>\r\n          <div className=\"p-4\">\r\n            <h2 className=\"text-lg font-semibold mb-2\">Lesson Title</h2>\r\n            <p className=\"text-gray-600 mb-4\">This is a brief description of the lesson content.</p>\r\n          </div>\r\n        </Card>\r\n        <Card className=' w-[250px] cursor-pointer overflow-hidden transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-xl'>\r\n          <div className=\"p-4\">\r\n            <h2 className=\"text-lg font-semibold mb-2\">Lesson Title</h2>\r\n            <p className=\"text-gray-600 mb-4\">This is a brief description of the lesson content.</p>\r\n          </div>\r\n        </Card>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default Dashboard\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,MAAM,YAAY;;IAChB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;YAC7D,aAAa;QACf;8BAAE,EAAE;IAEJ,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAK;0BACT,cAAA,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;;;;;;;;;;;YAMvC;0BACD,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAK;0BACT,cAAA,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;;;;;;;;;;;0BAMtC,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;;;;0BAGtC,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;;;;;;;;;;AAK9C;GA7CM;KAAA;uCA+CS", "debugId": null}}]}