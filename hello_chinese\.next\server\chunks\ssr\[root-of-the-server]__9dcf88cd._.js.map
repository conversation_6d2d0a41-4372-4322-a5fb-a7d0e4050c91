{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Hello_Chinese_HSK/hello_chinese/src/components/Quiz/Question.tsx"], "sourcesContent": ["type QuestionProps = {\r\n    text: string\r\n}\r\n\r\nexport default function Question({ text }: QuestionProps) {\r\n    return <h2 className=\"text-xl font-bold text-gray-800\">{text}</h2>\r\n}\r\n"], "names": [], "mappings": ";;;;;AAIe,SAAS,SAAS,EAAE,IAAI,EAAiB;IACpD,qBAAO,8OAAC;QAAG,WAAU;kBAAmC;;;;;;AAC5D", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Hello_Chinese_HSK/hello_chinese/src/components/Quiz/AnswerButton.tsx"], "sourcesContent": ["type AnswerButtonProps = {\r\n    text: string\r\n    onClick: () => void\r\n}\r\n\r\nexport default function AnswerButton({ text, onClick }: AnswerButtonProps) {\r\n    return (\r\n        <button\r\n            onClick={onClick}\r\n            className=\"w-full px-4 py-2 text-left bg-blue-100 hover:bg-blue-300 text-blue-900 rounded-lg transition\"\r\n        >\r\n            {text}\r\n        </button>\r\n    )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAKe,SAAS,aAAa,EAAE,IAAI,EAAE,OAAO,EAAqB;IACrE,qBACI,8OAAC;QACG,SAAS;QACT,WAAU;kBAET;;;;;;AAGb", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Hello_Chinese_HSK/hello_chinese/src/components/Quiz/QuizCard.tsx"], "sourcesContent": ["'use client'\r\nimport { useRef } from 'react'\r\nimport Question from './Question'\r\nimport AnswerButton from './AnswerButton'\r\n\r\ntype QuizCardProps = {\r\n    question: string\r\n    options: string[]\r\n    audioSrc: string\r\n    onSelect: (option: string) => void\r\n}\r\n\r\nexport default function QuizCard({ question, options, audioSrc, onSelect }: QuizCardProps) {\r\n    const audioRef = useRef<HTMLAudioElement | null>(null)\r\n\r\n    const playAudio = () => {\r\n        audioRef.current?.play()\r\n    }\r\n\r\n    \r\n    return (\r\n        <div className=\"p-6 space-y-6 max-w-xl w-full mx-auto\">\r\n            <div className=\"flex justify-between items-center\">\r\n                <Question text={question} />\r\n                <button onClick={playAudio} className=\"text-xl cursor-pointer\" title=\"Play Audio\">\r\n                    🔊\r\n                </button>\r\n            </div>\r\n            <div className=\"grid grid-cols-1 gap-2\">\r\n                {options.map((opt, idx) => (\r\n                    <AnswerButton key={idx} text={opt} onClick={() => onSelect(opt)} />\r\n                ))}\r\n            </div>\r\n            <audio ref={audioRef} src={audioSrc} hidden />\r\n        </div>\r\n    )\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;AAYe,SAAS,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAiB;IACrF,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA2B;IAEjD,MAAM,YAAY;QACd,SAAS,OAAO,EAAE;IACtB;IAGA,qBACI,8OAAC;QAAI,WAAU;;0BACX,8OAAC;gBAAI,WAAU;;kCACX,8OAAC,sIAAA,CAAA,UAAQ;wBAAC,MAAM;;;;;;kCAChB,8OAAC;wBAAO,SAAS;wBAAW,WAAU;wBAAyB,OAAM;kCAAa;;;;;;;;;;;;0BAItF,8OAAC;gBAAI,WAAU;0BACV,QAAQ,GAAG,CAAC,CAAC,KAAK,oBACf,8OAAC,0IAAA,CAAA,UAAY;wBAAW,MAAM;wBAAK,SAAS,IAAM,SAAS;uBAAxC;;;;;;;;;;0BAG3B,8OAAC;gBAAM,KAAK;gBAAU,KAAK;gBAAU,MAAM;;;;;;;;;;;;AAGvD", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Hello_Chinese_HSK/hello_chinese/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Hello_Chinese_HSK/hello_chinese/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Hello_Chinese_HSK/hello_chinese/src/app/quiz/page.tsx"], "sourcesContent": ["'use client'\r\nimport QuizCard from '@/components/Quiz/QuizCard'\r\nimport { useEffect, useState } from 'react'\r\nimport questions from '../../components/data/questions.json'\r\nimport { motion } from 'framer-motion'\r\nimport { Card } from '@/components/ui/card'\r\n\r\nexport default function QuizPage() {\r\n    const [current, setCurrent] = useState(0)\r\n    const [score, setScore] = useState(0)\r\n    const [totalAttempts, setTotalAttempts] = useState(0)\r\n    const [feedback, setFeedback] = useState<'correct' | 'wrong' | null>(null)\r\n    const [showResult, setShowResult] = useState(false)\r\n    const [timeLeft, setTimeLeft] = useState(10) // seconds\r\n\r\n    const handleAnswer = (option: string) => {\r\n        const isCorrect = option === questions[current].answer\r\n        setFeedback(isCorrect ? 'correct' : 'wrong')\r\n        if (isCorrect) setScore(score + 1)\r\n        setTotalAttempts(totalAttempts + 1)\r\n\r\n        setTimeout(() => {\r\n            setFeedback(null)\r\n            const next = current + 1\r\n            if (next < questions.length) {\r\n                setCurrent(next)\r\n                setTimeLeft(10)\r\n            } else {\r\n                setShowResult(true)\r\n            }\r\n        }, 1000)\r\n    }\r\n\r\n    useEffect(() => {\r\n        if (showResult) {\r\n            const result = {\r\n                score,\r\n                total: totalAttempts,\r\n                timestamp: new Date().toISOString(),\r\n            }\r\n            localStorage.setItem('chineseQuizResult', JSON.stringify(result))\r\n        }\r\n    }, [showResult, score, totalAttempts])\r\n\r\n    // Timer countdown logic\r\n    useEffect(() => {\r\n        if (timeLeft === 0) {\r\n            handleAnswer('') // skip if no answer\r\n            return\r\n        }\r\n        const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000)\r\n        return () => clearTimeout(timer)\r\n    }, [timeLeft])\r\n\r\n    const resetQuiz = () => {\r\n        setCurrent(0)\r\n        setScore(0)\r\n        setTotalAttempts(0)\r\n        setShowResult(false)\r\n        setTimeLeft(10)\r\n        setFeedback(null)\r\n    }\r\n\r\n    if (showResult) {\r\n        return (\r\n            <div className='flex items-center justify-center my-10'>\r\n                <Card className=\"w-[450px] shadow-xl justify-center items-center p-6 text-center space-y-4\">\r\n                    <h2 className=\"text-2xl font-bold\">🎉 Quiz Complete!</h2>\r\n                    <p className=\"text-lg\">✅ Correct: {score}</p>\r\n                    <p className=\"text-lg\">❌ Incorrect: {totalAttempts - score}</p>\r\n                    <p className=\"text-lg\">📊 Score: {score}/{questions.length}</p>\r\n                    <button\r\n                        onClick={resetQuiz}\r\n                        className=\"mt-4 px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition cursor-pointer\"\r\n                    >\r\n                        🔁 Try Again\r\n                    </button>\r\n                </Card>\r\n\r\n            </div>\r\n        )\r\n    }\r\n\r\n    const { question, options, audio } = questions[current]\r\n\r\n    return (\r\n        <div className=\"flex items-center justify-center my-5\">\r\n            <Card className=\"justify-center p-1 flex-col w-[450px] h-[500px] shadow-xl\">\r\n\r\n                <p className=\"text-lg mt-1 text-center font-semibold\">Question {current + 1} / {questions.length}</p>\r\n\r\n                {/* Progress Bar */}\r\n                <div className=\"w-[400px] max-w-xl bg-gray-200 h-2 rounded-full mb-1 mx-auto\">\r\n                    <div\r\n                        className=\"bg-blue-500 h-2 rounded-full transition-all duration-300\"\r\n                        style={{ width: `${((current + 1) / questions.length) * 100}%` }}\r\n                    ></div>\r\n                </div>\r\n\r\n                {/* Timer */}\r\n                <p className=\"text-sm text-center text-gray-500 -translate-y-3\">⏳ Time left: {timeLeft}s</p>\r\n\r\n                {/* Quiz Content */}\r\n                <div className={`transition duration-300 transform -translate-y-8 ${feedback === 'correct' ? 'scale-105' : feedback === 'wrong' ? 'scale-95' : ''}`}>\r\n                    <motion.div\r\n                        key={current}\r\n                        initial={{ opacity: 0, y: 20 }}\r\n                        animate={{ opacity: 1, y: 0 }}\r\n                        exit={{ opacity: 0, y: -20 }}\r\n                        transition={{ duration: 0.3 }}\r\n                    >\r\n                        <QuizCard\r\n                            question={question}\r\n                            options={options}\r\n                            audioSrc={audio}\r\n                            onSelect={handleAnswer}\r\n                        />\r\n                    </motion.div>\r\n                    {feedback && (\r\n                        <div className={`-mt-5 text-center font-semibold ${feedback === 'correct' ? 'text-green-600' : 'text-red-600'}`}>\r\n                            {feedback === 'correct' ? '✅ Correct!' : '❌ Wrong'}\r\n                        </div>\r\n                    )}\r\n                </div>\r\n            </Card>\r\n        </div>\r\n    )\r\n}\r\n\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AALA;;;;;;;AAOe,SAAS;IACpB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8B;IACrE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,UAAU;;IAEvD,MAAM,eAAe,CAAC;QAClB,MAAM,YAAY,WAAW,8GAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,MAAM;QACtD,YAAY,YAAY,YAAY;QACpC,IAAI,WAAW,SAAS,QAAQ;QAChC,iBAAiB,gBAAgB;QAEjC,WAAW;YACP,YAAY;YACZ,MAAM,OAAO,UAAU;YACvB,IAAI,OAAO,8GAAA,CAAA,UAAS,CAAC,MAAM,EAAE;gBACzB,WAAW;gBACX,YAAY;YAChB,OAAO;gBACH,cAAc;YAClB;QACJ,GAAG;IACP;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,YAAY;YACZ,MAAM,SAAS;gBACX;gBACA,OAAO;gBACP,WAAW,IAAI,OAAO,WAAW;YACrC;YACA,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;QAC7D;IACJ,GAAG;QAAC;QAAY;QAAO;KAAc;IAErC,wBAAwB;IACxB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,aAAa,GAAG;YAChB,aAAa,IAAI,oBAAoB;;YACrC;QACJ;QACA,MAAM,QAAQ,WAAW,IAAM,YAAY,WAAW,IAAI;QAC1D,OAAO,IAAM,aAAa;IAC9B,GAAG;QAAC;KAAS;IAEb,MAAM,YAAY;QACd,WAAW;QACX,SAAS;QACT,iBAAiB;QACjB,cAAc;QACd,YAAY;QACZ,YAAY;IAChB;IAEA,IAAI,YAAY;QACZ,qBACI,8OAAC;YAAI,WAAU;sBACX,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACZ,8OAAC;wBAAG,WAAU;kCAAqB;;;;;;kCACnC,8OAAC;wBAAE,WAAU;;4BAAU;4BAAY;;;;;;;kCACnC,8OAAC;wBAAE,WAAU;;4BAAU;4BAAc,gBAAgB;;;;;;;kCACrD,8OAAC;wBAAE,WAAU;;4BAAU;4BAAW;4BAAM;4BAAE,8GAAA,CAAA,UAAS,CAAC,MAAM;;;;;;;kCAC1D,8OAAC;wBACG,SAAS;wBACT,WAAU;kCACb;;;;;;;;;;;;;;;;;IAOjB;IAEA,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,8GAAA,CAAA,UAAS,CAAC,QAAQ;IAEvD,qBACI,8OAAC;QAAI,WAAU;kBACX,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BAEZ,8OAAC;oBAAE,WAAU;;wBAAyC;wBAAU,UAAU;wBAAE;wBAAI,8GAAA,CAAA,UAAS,CAAC,MAAM;;;;;;;8BAGhG,8OAAC;oBAAI,WAAU;8BACX,cAAA,8OAAC;wBACG,WAAU;wBACV,OAAO;4BAAE,OAAO,GAAG,AAAC,CAAC,UAAU,CAAC,IAAI,8GAAA,CAAA,UAAS,CAAC,MAAM,GAAI,IAAI,CAAC,CAAC;wBAAC;;;;;;;;;;;8BAKvE,8OAAC;oBAAE,WAAU;;wBAAmD;wBAAc;wBAAS;;;;;;;8BAGvF,8OAAC;oBAAI,WAAW,CAAC,iDAAiD,EAAE,aAAa,YAAY,cAAc,aAAa,UAAU,aAAa,IAAI;;sCAC/I,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAEP,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,YAAY;gCAAE,UAAU;4BAAI;sCAE5B,cAAA,8OAAC,sIAAA,CAAA,UAAQ;gCACL,UAAU;gCACV,SAAS;gCACT,UAAU;gCACV,UAAU;;;;;;2BAVT;;;;;wBAaR,0BACG,8OAAC;4BAAI,WAAW,CAAC,gCAAgC,EAAE,aAAa,YAAY,mBAAmB,gBAAgB;sCAC1G,aAAa,YAAY,eAAe;;;;;;;;;;;;;;;;;;;;;;;AAOrE", "debugId": null}}]}