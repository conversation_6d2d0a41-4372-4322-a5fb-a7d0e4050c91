
import { Card } from '@/components/ui/card'
import Link from 'next/link'
import React from 'react'

const Dashboard = () => {
  return (
    <div className=" flex flex-col md:flex-row gap-4 justify-center items-center px-4 md:px-10 md:mt-32 mt-10 ">
      <Link href='/all_lesson'>
        <Card className=' w-[250px] cursor-pointer overflow-hidden transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-xl'>
          <div className="p-4">
            <h2 className="text-lg font-semibold mb-2">All Lessons</h2>
            <p className="text-gray-600 mb-4">
              Explore all available lessons to enhance your Chinese language skills.
            </p>
          </div>
        </Card>
      </Link>
      <Link href='/quiz'>
        <Card className=' w-[250px] cursor-pointer overflow-hidden transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-xl'>
          <div className="p-4">
            <h2 className="text-lg font-semibold mb-2">Lesson Quiz</h2>
            <p className="text-gray-600 mb-4">
              Test your knowledge with quizzes based on the lessons.
            </p>
          </div>
        </Card>
      </Link>
        <Card className=' w-[250px] cursor-pointer overflow-hidden transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-xl'>
          <div className="p-4">
            <h2 className="text-lg font-semibold mb-2">Lesson Title</h2>
            <p className="text-gray-600 mb-4">This is a brief description of the lesson content.</p>
          </div>
        </Card>
        <Card className=' w-[250px] cursor-pointer overflow-hidden transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-xl'>
          <div className="p-4">
            <h2 className="text-lg font-semibold mb-2">Lesson Title</h2>
            <p className="text-gray-600 mb-4">This is a brief description of the lesson content.</p>
          </div>
        </Card>
    </div>
  )
}

export default Dashboard
