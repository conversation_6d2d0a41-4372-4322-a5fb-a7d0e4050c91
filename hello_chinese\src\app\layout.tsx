import type { Metadata } from "next";
import "./globals.css";
import Navbar from "@/components/Header";

export const metadata: Metadata = {
  title: "Hello Chinese",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body>
        <div className="flex flex-col min-h-screen">
          <Navbar />
          <main className="flex-grow p-6">{children}</main>
        </div>
      </body>
    </html>
  );
}
