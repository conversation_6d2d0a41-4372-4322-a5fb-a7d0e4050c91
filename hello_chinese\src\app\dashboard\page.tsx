'use client'

import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogOverlay, AlertDialogPortal, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog'
import { Card } from '@/components/ui/card'
import Link from 'next/link'
import React from 'react'

const Dashboard = () => {

  return (
    <div className=" flex flex-col md:flex-row gap-4 justify-center items-center px-4 md:px-10 md:mt-32 mt-10 ">
      <Link href='/all_lesson'>
        <Card className=' w-[250px] cursor-pointer overflow-hidden transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-xl'>
          <div className="p-4">
            <h2 className="text-lg font-semibold mb-2">All Lessons</h2>
            <p className="text-gray-600 mb-4">
              Explore all available lessons to enhance your Chinese language skills.
            </p>
          </div>
        </Card>
      </Link>

      {/* Alert Dialog for Quiz */}
      <AlertDialog>
        <AlertDialogTrigger>
          <Card className='w-[250px] cursor-pointer overflow-hidden transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-xl'>
            <div className="p-4">
              <h2 className="text-lg font-semibold mb-2">Lesson Quiz</h2>
              <p className="text-gray-600 mb-4">
                Test your knowledge with quizzes based on the lessons.
              </p>
            </div>
          </Card>
        </AlertDialogTrigger>
        <AlertDialogPortal>
          <AlertDialogOverlay className="fixed inset-0 bg-black bg-opacity-50" />
          <AlertDialogContent className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white p-6 rounded-lg shadow-lg w-[90%] max-w-md">
            <AlertDialogTitle className="text-lg font-semibold">Start Quiz</AlertDialogTitle>
            <AlertDialogDescription className="mt-2 text-gray-600">
              Do you want to start the quiz?
            </AlertDialogDescription>
            <div className="mt-4 flex justify-end gap-4">
              <AlertDialogCancel asChild>
                <button className="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300 cursor-pointer ">Cancel</button>
              </AlertDialogCancel>
              <AlertDialogAction asChild>
                <Link href="/quiz">
                  <button className="px-4 py-2 text-white rounded cursor-pointer">
                    Start Quiz
                  </button>
                </Link>
              </AlertDialogAction>
            </div>
          </AlertDialogContent>
        </AlertDialogPortal>
      </AlertDialog>
        <Card className=' w-[250px] cursor-pointer overflow-hidden transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-xl'>
          <div className="p-4">
            <h2 className="text-lg font-semibold mb-2">Lesson Title</h2>
            <p className="text-gray-600 mb-4">This is a brief description of the lesson content.</p>
          </div>
        </Card>
        <Card className=' w-[250px] cursor-pointer overflow-hidden transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-xl'>
          <div className="p-4">
            <h2 className="text-lg font-semibold mb-2">Lesson Title</h2>
            <p className="text-gray-600 mb-4">This is a brief description of the lesson content.</p>
          </div>
        </Card>
    </div>
  )
}

export default Dashboard